# Salmate-Deployment-Guide

This repository contains the deployment setup for the application using Docker, Traefik, and environment configurations.

- [Salmate-Deployment-Guide](#salmate-deployment-guide)
  - [To Deploy](#to-deploy)
    - [Prerequisites](#prerequisites)
    - [Setup Environment Variables](#setup-environment-variables)
      - [Frontend Service](#frontend-service)
      - [Backend Service](#backend-service)
      - [LLM Service](#llm-service)
    - [Build and Run Containers](#build-and-run-containers)
    - [Verify Deployment](#verify-deployment)
  - [Troubleshooting](#troubleshooting)

## To Deploy

### Prerequisites

Ensure you have the following installed on your deployment machine:

- Docker & Docker Compose
- A domain name configured with DNS pointing to the server
- SSL certificates (Let's Encrypt will be used via Traefik)

### Setup Environment Variables
```
cp file.env .env
cp file.openai.key .openai.key 
```

Create a `.env` file in the root directory and configure the required values:

#### Frontend Service
`.env`
```sh
PUBLIC_BACKEND_URL= 
PUBLIC_BUILD_VERSION= 
ORIGIN= 
...
```

#### Backend Service
`.env`
```sh
REDIS_HOST=
REDIS_PORT=
DB_HOST=
DB_PORT=
DB_NAME=
DB_USER=
DB_PASS= 
LINE_ACCESS_TOKEN= 
LINE_CHANNEL_SECRET= 
LANGSERVE_SCHEME=
LANGSERVE_HOST=
LANGSERVE_PORT=
ALLOWED_HOSTS=
NEARLY_EXPIRED_DATE=
INACTIVE_TICKET_TIME_MINUTES=
AZURE_ACCOUNT_NAME= 
AZURE_ACCOUNT_KEY= 
AZURE_CONTAINER= 
...
```

#### LLM Service
`.env`
```sh
# Set this to `"true"` if you want to enable `LangSmith`
# Then, provide the `LANGCHAIN_API_KEY` below with the `PROJECT
LANGCHAIN_TRACING_V2="false"
# LANGCHAIN_API_KEY="<YOUR-API-KEY>"  # Update to your API key
LANGCHAIN_PROJECT= 
LANGCHAIN_ENDPOINT=
ALLOW_ORIGINS=
```

`.openai.key `

```sh
OPENAI_API_VERSION=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
```

### Build and Run Containers

1. Start the Traefik reverse proxy:

   ```sh
   docker compose up -d
   ```

2. Deploy the application container:

   ```sh
   docker compose up -d
   ```

### Verify Deployment

Check if the containers are running:

```sh
docker ps
```

Ensure your application is accessible via `https://your-domain.com`.

## Troubleshooting

- **Container fails to start?** Check logs with:
  ```sh
  docker logs <container_name>
  ```
- **SSL certificate issues?** Ensure Traefik is configured correctly with Let's Encrypt.
- **Network issues?** Ensure the `traefik_default` network exists and is correctly linked.
  ```sh
  docker network ls
  ```

