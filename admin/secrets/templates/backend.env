# Backend Server
BACKEND_HOST=

# Redis Configuration
REDIS_HOST=
REDIS_PORT=

# Database Configuration
DB_HOST=
DB_PORT=
DB_NAME=
DB_USER=
DB_PASS=

# LINE Messaging API (Fill in your LINE credentials)
LINE_ACCESS_TOKEN=
LINE_CHANNEL_SECRET=

# LangServe Configuration (Fill if using LangServe)
LANGSERVE_SCHEME=
LANGSERVE_HOST=
LANGSERVE_PORT=

# Allowed Hosts (Comma-separated)
ALLOWED_HOSTS=

# LLM Endpoints (Fill your own service URLs)
LLM_INTEND=
LLM_DEFAULT=
LLM_FAQ=
LLM_RECOMMENDATION=
SERVICE_VECTOR_DB=
LLM_SALMATE_LANGGRAPH=

# Ticket Settings
INACTIVE_TICKET_1ST_TIME_MINUTES=
INACTIVE_TICKET_2ND_TIME_MINUTES=
INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES=

# Celery Settings
CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES=
CELERY_CHECK_DATABACKUP_AT_HOUR=

# Miscellaneous
NEARLY_EXPIRED_DATE=

# Azure Blob Storage (Fill in Azure Storage details)
AZURE_ACCOUNT_NAME=
AZURE_ACCOUNT_KEY=
AZURE_CONTAINER=

# API URLs (Fill in your API endpoints)
ANALYSIS_API_URL=
VECTORDB_API_URL=
