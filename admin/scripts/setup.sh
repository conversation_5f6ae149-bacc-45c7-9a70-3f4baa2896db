#!/bin/bash
set -e
set -o pipefail

# Configuration variables
# Docker and deployment settings
TAG="v1.0.0"
DOCKER_REGISTRY="ghcr.io"
DOCKER_TIMEOUT=30
BACKEND_INIT_WAIT=10

# Repository and Git settings
GITHUB_REPO_URL="https://github.com/AI-Brainlab/salmate-production.git"
GIT_CLONE_DEPTH=1
GIT_TIMEOUT=30

# Secrets and configuration paths
SECRETS_DIR="$HOME/admin/secrets"
SECRETS_MAP_FILE="secrets-map.json"

# Temporary and progress tracking files
TEMP_REPO_DIR="$HOME/temp_git/repo"
PROGRESS_FILE="$HOME/.salmate_progress"
LOG_FILE="$HOME/.salmate_setup.log"

# Domain settings
SUBDOMAIN=""
DOMAIN_SUFFIX="aibrainlab.co"

# System settings
TIMEZONE="Asia/Bangkok"
DEFAULT_FILE_PERMISSIONS=600
MAX_LOG_SIZE_KB=1024

# Git Credential Manager settings
GCM_FALLBACK_VERSION="v2.3.2"

# Service directories
TRAEFIK_DIR="$HOME/_traefik"
POSTGRES_DIR="$HOME/postgres"
REDIS_DIR="$HOME/redis"
LLM_DIR="$HOME/llm"
BACKEND_DIR="$HOME/backend"
FRONTEND_DIR="$HOME/frontend"
MONITORING_DIR="$HOME/monitoring"

# Set timezone
sudo timedatectl set-timezone $TIMEZONE

# Function to validate Docker image tag
validate_tag() {
    local image_name="$1"
    local tag="$2"
    local service="$3"

    log "Validating tag: $tag for image: $image_name ($service)"

    # Check if the tag exists by attempting to pull it
    if ! docker pull "$image_name:$tag" &> /dev/null; then
        log "❌ Error: Tag '$tag' does not exist for image '$image_name'"
        log "Please update the TAG variable at the top of this script with a valid tag."
        exit 1
    fi

    log "✅ Tag validation successful for $service"
    return 0
}

# Function to extract image name from docker-compose file
extract_image_name() {
    local service_dir="$1"
    local service_name="$2"

    # Determine the image name from docker-compose.yml
    if [ -f "$service_dir/docker-compose.yml" ]; then
        compose_file="$service_dir/docker-compose.yml"
    elif [ -f "$service_dir/docker-compose.yaml" ]; then
        compose_file="$service_dir/docker-compose.yaml"
    else
        log "❌ Error: No docker-compose.yml or docker-compose.yaml found in $service_dir"
        exit 1
    fi

    # Extract image name from docker-compose file
    image_line=$(grep -E "^\s*image:" "$compose_file" | head -n 1)
    image_name=$(echo "$image_line" | sed -E 's/^\s*image:\s*([^:$]+)(:.+)?$/\1/' | tr -d '"' | tr -d "'")

    if [ -z "$image_name" ]; then
        log "❌ Error: Could not determine image name from $compose_file"
        exit 1
    fi

    # Validate the tag
    validate_tag "$image_name" "$TAG" "$service_name"

    echo "$image_name"
}

# Function to check log file size and rotate if necessary
check_log_size() {
    if [ -f "$LOG_FILE" ]; then
        # Get file size in KB
        local size_kb=$(du -k "$LOG_FILE" | cut -f1)

        if [ "$size_kb" -gt "$MAX_LOG_SIZE_KB" ]; then
            local timestamp=$(date '+%Y%m%d_%H%M%S')
            local backup_file="${LOG_FILE}.${timestamp}"

            # Rotate the log file
            mv "$LOG_FILE" "$backup_file"

            # Create a new log file with header
            echo "=== Salmate Setup Log - Rotated on $(date '+%Y-%m-%d %H:%M:%S') ===" > "$LOG_FILE"
            echo "=== Previous log rotated to $(basename "$backup_file") ===" >> "$LOG_FILE"
            chmod $DEFAULT_FILE_PERMISSIONS "$LOG_FILE"

            # Inform user
            echo "Log file rotated to $backup_file due to size limit ($MAX_LOG_SIZE_KB KB)"
        fi
    fi
}

# Function to execute a command and capture its output for logging
run_with_log() {
    local cmd="$1"
    local step_name="$2"
    local exit_on_error="${3:-true}"  # Default to true if not provided
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Log the command being executed
    log "Executing: $cmd"
    echo "[$timestamp] COMMAND: $cmd" >> "$LOG_FILE"

    # Create a temporary file for output
    local output_file=$(mktemp)

    # Execute the command and capture output and exit code
    eval "$cmd" > "$output_file" 2>&1
    local exit_code=$?

    # Get the command output
    local cmd_output=$(cat "$output_file")

    # Log the output to the log file
    echo "[$timestamp] OUTPUT START ----------------------------------------" >> "$LOG_FILE"
    echo "$cmd_output" >> "$LOG_FILE"
    echo "[$timestamp] OUTPUT END ------------------------------------------" >> "$LOG_FILE"

    # Clean up
    rm -f "$output_file"

    # Return the exit code for verify_command to use
    return $exit_code
}

# Function to log messages with timestamps
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local message="[$timestamp] $1"

    # Print to console
    echo "$message"

    # Create log directory if it doesn't exist
    local log_dir=$(dirname "$LOG_FILE")
    if [ ! -d "$log_dir" ]; then
        mkdir -p "$log_dir"
    fi

    # Check log size and rotate if necessary
    check_log_size

    # Append to log file
    echo "$message" >> "$LOG_FILE"
}

# Function to verify command execution with enhanced logging
verify_command() {
    local step_name="$1"
    local exit_on_error="${2:-true}"  # Default to true if not provided
    local exit_code=$?
    local current_dir=$(pwd)
    local user=$(whoami)
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Log detailed information about the command execution
    if [ $exit_code -eq 0 ]; then
        log "$(echo -e "\e[32m✅ $step_name completed successfully\e[0m")"
        # Add more detailed information to log file only
        echo "[$timestamp] DETAILS: Step '$step_name' completed with exit code 0" >> "$LOG_FILE"
        echo "[$timestamp] DETAILS: Current directory: $current_dir" >> "$LOG_FILE"
        echo "[$timestamp] DETAILS: User: $user" >> "$LOG_FILE"

        # Mark this step as completed in the progress file
        echo "$step_name" >> "$PROGRESS_FILE"
        return 0
    else
        # Get system information for troubleshooting
        local memory=$(free -m | grep Mem | awk '{print "Total: " $2 "MB, Used: " $3 "MB, Free: " $4 "MB"}')
        local disk=$(df -h / | tail -1 | awk '{print "Total: " $2 ", Used: " $3 " (" $5 "), Free: " $4}')

        # Log detailed error information to log file
        echo "[$timestamp] ERROR DETAILS: Step '$step_name' failed with exit code $exit_code" >> "$LOG_FILE"
        echo "[$timestamp] ERROR DETAILS: Current directory: $current_dir" >> "$LOG_FILE"
        echo "[$timestamp] ERROR DETAILS: User: $user" >> "$LOG_FILE"
        echo "[$timestamp] ERROR DETAILS: Memory: $memory" >> "$LOG_FILE"
        echo "[$timestamp] ERROR DETAILS: Disk space: $disk" >> "$LOG_FILE"

        if [ "$exit_on_error" = "true" ]; then
            log "❌ $step_name failed with exit code $exit_code - exiting script"
            echo "[$timestamp] CRITICAL: Script execution terminated due to error in step '$step_name'" >> "$LOG_FILE"
            exit 1
        else
            log "⚠️ $step_name failed with exit code $exit_code - continuing anyway"
            echo "[$timestamp] WARNING: Continuing execution despite error in step '$step_name'" >> "$LOG_FILE"
            # Still mark as completed to avoid retrying in future runs
            echo "$step_name" >> "$PROGRESS_FILE"
            return 1
        fi
    fi
}

# Function to check if a step has been completed
is_step_completed() {
    local step="$1"
    if [ -f "$PROGRESS_FILE" ] && grep -q "^$step$" "$PROGRESS_FILE"; then
        return 0  # Step is completed
    else
        return 1  # Step is not completed
    fi
}

# Function to handle script interruptions and resuming with enhanced logging
handle_interruption() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local current_dir=$(pwd)
    local user=$(whoami)

    log "⚠️ Script was interrupted. You can resume from where you left off by running the script again."

    # Log detailed information about the interruption to the log file
    echo "[$timestamp] INTERRUPTION: Script was manually interrupted by user" >> "$LOG_FILE"
    echo "[$timestamp] INTERRUPTION DETAILS: Current directory: $current_dir" >> "$LOG_FILE"
    echo "[$timestamp] INTERRUPTION DETAILS: User: $user" >> "$LOG_FILE"

    # If we have a progress file, log how many steps were completed
    if [ -f "$PROGRESS_FILE" ]; then
        local completed_steps=$(wc -l < "$PROGRESS_FILE")
        echo "[$timestamp] INTERRUPTION DETAILS: $completed_steps steps were completed before interruption" >> "$LOG_FILE"
        echo "[$timestamp] INTERRUPTION DETAILS: Last completed step: $(tail -1 "$PROGRESS_FILE")" >> "$LOG_FILE"
    else
        echo "[$timestamp] INTERRUPTION DETAILS: No steps were completed before interruption" >> "$LOG_FILE"
    fi

    echo "[$timestamp] RESUMING: To resume, run this script again" >> "$LOG_FILE"
    exit 1
}

# Set up trap for interruption signals
trap handle_interruption INT TERM

# Function to add or update a variable in the config file
set_config() {
    local key=$1
    local value=$2

    # Create config file if it doesn't exist
    if [ ! -f "$CONFIG_FILE" ]; then
        touch "$CONFIG_FILE"
        chmod $DEFAULT_FILE_PERMISSIONS "$CONFIG_FILE"  # Secure permissions
    fi

    # Check if key exists and update, otherwise add
    # Using grep with fixed strings and word boundaries for more accurate matching
    if grep -q "^$key=" "$CONFIG_FILE"; then
        # Use sed with a different delimiter to avoid issues with values containing slashes
        # This syntax is compatible with Ubuntu 22.04's sed
        sed -i "s/^$key=.*/$key=\"$value\"/" "$CONFIG_FILE"
    else
        echo "$key=\"$value\"" >> "$CONFIG_FILE"
    fi

    # Also export to current environment
    export "$key"="$value"
}

# Function to get a variable from the config file
get_config() {
    local key=$1
    local value

    if [ -f "$CONFIG_FILE" ]; then
        value=$(grep "^$key=" "$CONFIG_FILE" | cut -d '=' -f2- | tr -d '"')
        echo "$value"
    fi
}

# Function to extract subdomain from hostname
get_subdomain() {
    # Extract the first part of the hostname (before first dash)
    hostname=$(hostname)
    subdomain=$(echo "$hostname" | cut -d'-' -f1,2)
    echo "$subdomain"
}

# Function to log system information
log_system_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "[$timestamp] SYSTEM INFO: Collecting system information..." >> "$LOG_FILE"

    # OS Information
    echo "[$timestamp] OS INFO: $(lsb_release -ds 2>/dev/null || cat /etc/*release 2>/dev/null | head -n1 || uname -om)" >> "$LOG_FILE"

    # Kernel version
    echo "[$timestamp] KERNEL: $(uname -r)" >> "$LOG_FILE"

    # CPU information
    echo "[$timestamp] CPU: $(grep "model name" /proc/cpuinfo | head -1 | cut -d':' -f2 | sed 's/^[ \t]*//')" >> "$LOG_FILE"
    echo "[$timestamp] CPU CORES: $(grep -c processor /proc/cpuinfo)" >> "$LOG_FILE"

    # Memory information
    local memory=$(free -h | grep Mem)
    echo "[$timestamp] MEMORY: Total: $(echo $memory | awk '{print $2}'), Used: $(echo $memory | awk '{print $3}'), Free: $(echo $memory | awk '{print $4}')" >> "$LOG_FILE"

    # Disk information
    echo "[$timestamp] DISK USAGE:" >> "$LOG_FILE"
    df -h | grep -v "tmpfs\|udev" >> "$LOG_FILE"

    # Network information
    echo "[$timestamp] NETWORK INTERFACES:" >> "$LOG_FILE"
    ip -o addr show | grep -v "lo\|docker" | awk '{print $2 ": " $4}' >> "$LOG_FILE"

    # Hostname
    echo "[$timestamp] HOSTNAME: $(hostname)" >> "$LOG_FILE"

    # Current user
    echo "[$timestamp] USER: $(whoami)" >> "$LOG_FILE"

    # Current directory
    echo "[$timestamp] CURRENT DIRECTORY: $(pwd)" >> "$LOG_FILE"

    # Environment variables (excluding sensitive information)
    echo "[$timestamp] ENVIRONMENT VARIABLES:" >> "$LOG_FILE"
    env | grep -v "TOKEN\|PASSWORD\|SECRET\|KEY" | sort >> "$LOG_FILE"

    echo "[$timestamp] SYSTEM INFO: System information collection completed" >> "$LOG_FILE"
}

# Initialize log file with header
if [ -f "$LOG_FILE" ]; then
    echo "=== Continuing setup on $(date '+%Y-%m-%d %H:%M:%S') ===" >> "$LOG_FILE"
else
    echo "=== Salmate Setup Log - Started on $(date '+%Y-%m-%d %H:%M:%S') ===" > "$LOG_FILE"
    chmod $DEFAULT_FILE_PERMISSIONS "$LOG_FILE"
fi

# Inform user about log file
echo "Logging to file: $LOG_FILE"

# Log system information
log_system_info

# Check if .salmate_progress file exists, which indicates a previous installation
if [ -f "$PROGRESS_FILE" ]; then
    # Check if this is a fresh run or if we're trying to run the script again on an existing installation
    COMPLETED_STEPS=$(wc -l < "$PROGRESS_FILE")

    # If there are completed steps, this is likely an existing installation
    if [ "$COMPLETED_STEPS" -gt 0 ]; then
        log "❌ Detected existing installation with $COMPLETED_STEPS completed steps."
        log "❌ Please run cleanup.sh first before attempting a new installation."
        log "❌ Command: ./cleanup.sh or admin/scripts/cleanup.sh"
        log "❌ Exiting script to prevent conflicts with existing installation."
        exit 1
    else
        log "Found empty progress file. Resuming post-deployment setup..."
        log "To start fresh, delete $PROGRESS_FILE and run the script again."
    fi
else
    log "Starting post-deployment setup..."
    # Create empty progress file
    touch "$PROGRESS_FILE"
    chmod $DEFAULT_FILE_PERMISSIONS "$PROGRESS_FILE"
fi

# Ensure script is run with bash
if [ -z "$BASH_VERSION" ]; then
    echo "This script must be run with bash, not sh or other shells."
    exit 1
fi

# Check if running on Ubuntu 22.04
if [ -f /etc/os-release ]; then
    . /etc/os-release
    if [ "$ID" != "ubuntu" ] || [ "$VERSION_ID" != "22.04" ]; then
        echo "⚠️ Warning: This script is designed for Ubuntu 22.04."
        echo "Current OS: $PRETTY_NAME"
        echo "Some commands may not work as expected."
        echo "Press Ctrl+C to abort or Enter to continue..."
        read -r
    fi
fi

# System updates with automatic handling of prompts
if ! is_step_completed "System update"; then
    log "Updating system packages..."
    run_with_log "sudo apt-get -y update" "System update"
    verify_command "System update"
else
    log "Skipping system update (already completed)"
fi

# Set environment variables to prevent interactive prompts
export DEBIAN_FRONTEND=noninteractive

# Pre-configure packages to automatically restart services
if ! is_step_completed "Pre-configure packages"; then
    log "Pre-configuring packages to handle service restarts..."
    # Configure all services to restart automatically without asking
    echo '* libraries/restart-without-asking boolean true' | sudo debconf-set-selections
    # Specific configurations for common packages
    echo 'libssl1.1 libraries/restart-without-asking boolean true' | sudo debconf-set-selections
    echo 'libssl1.0.0 libraries/restart-without-asking boolean true' | sudo debconf-set-selections
    verify_command "Pre-configure packages"
else
    log "Skipping package pre-configuration (already completed)"
fi

# Perform upgrade with options to handle interactive prompts
if ! is_step_completed "System upgrade"; then
    log "Upgrading system packages (this may take a while)..."
    run_with_log "sudo apt-get -y update" "System update refresh"
    run_with_log "sudo DEBIAN_FRONTEND=noninteractive apt-get -y -o Dpkg::Options::=\"--force-confnew\" --fix-missing upgrade" "System upgrade"    verify_command "System upgrade"
    verify_command "System upgrade" false
else
    log "Skipping system upgrade (already completed)"
fi

# Reset the frontend
export DEBIAN_FRONTEND=dialog

# Install Git and Git Credential Manager
if ! is_step_completed "Git installation"; then
    log "Installing Git..."
    sudo DEBIAN_FRONTEND=noninteractive apt-get -y install git
    verify_command "Git installation"

    # Check if Git is installed correctly
    git --version
    verify_command "Git version check"
else
    log "Skipping Git installation (already completed)"
fi

# Install Git Credential Manager for Ubuntu 22.04
if ! is_step_completed "Git Credential Manager installation"; then
    log "Installing Git Credential Manager..."

    # Install dependencies with non-interactive options
    sudo DEBIAN_FRONTEND=noninteractive apt-get -y install libsecret-1-0 pass gnupg2

    # For Ubuntu 22.04, we'll use a more robust approach for GCM
    log "Checking for Git Credential Manager..."

    # First check if GCM is already installed
    if command -v git-credential-manager &> /dev/null || command -v git-credential-manager-core &> /dev/null; then
        log "Git Credential Manager is already installed."
    else
        log "Git Credential Manager not found, installing..."

        # Try to get the latest version, with error handling
        GCM_VERSION=""
        GCM_VERSION_FETCH=$(curl -s -f --connect-timeout 10 --max-time 30 https://api.github.com/repos/GitCredentialManager/git-credential-manager/releases/latest || echo "FAILED")

        if [[ "$GCM_VERSION_FETCH" == "FAILED" ]]; then
            log "⚠️ Failed to fetch latest GCM version from GitHub API. Using fallback version."
            GCM_VERSION="$GCM_FALLBACK_VERSION"  # Use fallback version
        else
            GCM_VERSION=$(echo "$GCM_VERSION_FETCH" | grep -oP '"tag_name": "\K(.*)(?=")' || echo "$GCM_FALLBACK_VERSION")
            log "Latest GCM version: $GCM_VERSION"
        fi

        # Ensure we have a version
        if [[ -z "$GCM_VERSION" ]]; then
            GCM_VERSION="$GCM_FALLBACK_VERSION"  # Use fallback version if parsing failed
            log "⚠️ Using fallback GCM version: $GCM_VERSION"
        fi

        GCM_DEB_NAME="gcm-linux_amd64.${GCM_VERSION:1}.deb"
        GCM_URL="https://github.com/GitCredentialManager/git-credential-manager/releases/download/${GCM_VERSION}/${GCM_DEB_NAME}"

        log "Downloading Git Credential Manager ${GCM_VERSION} from ${GCM_URL}..."

        # Download with error handling
        if ! curl -L -f --connect-timeout 10 --max-time 60 -o "$GCM_DEB_NAME" "$GCM_URL"; then
            log "⚠️ Failed to download Git Credential Manager. Skipping installation."
            log "You can manually install it later if needed."
        else
            log "Successfully downloaded $GCM_DEB_NAME"

            # Check if the file exists and has a non-zero size
            if [ ! -s "$GCM_DEB_NAME" ]; then
                log "⚠️ Downloaded file is empty or does not exist. Skipping installation."
            else
                # Install the package
                if ! sudo DEBIAN_FRONTEND=noninteractive dpkg -i "${GCM_DEB_NAME}"; then
                    log "⚠️ Failed to install Git Credential Manager package."

                    # Try to fix broken dependencies
                    log "Attempting to fix dependencies..."
                    sudo DEBIAN_FRONTEND=noninteractive apt-get -f -y install

                    # Try installing again
                    if ! sudo DEBIAN_FRONTEND=noninteractive dpkg -i "${GCM_DEB_NAME}"; then
                        log "⚠️ Failed to install Git Credential Manager after fixing dependencies."
                    else
                        log "Successfully installed Git Credential Manager after fixing dependencies."
                    fi
                else
                    log "Successfully installed Git Credential Manager."
                fi
            fi

            # Clean up
            rm -f "${GCM_DEB_NAME}"

            # Configure Git to use the credential manager
            git config --global credential.helper manager || log "⚠️ Failed to configure Git credential helper."
        fi
    fi

    # Verify installation - with extra error handling
    if command -v git-credential-manager &> /dev/null; then
        log "Using git-credential-manager:"
        # Run with timeout to prevent hanging
        timeout 10 git-credential-manager --version || log "⚠️ Failed to get git-credential-manager version."
    elif command -v git-credential-manager-core &> /dev/null; then
        log "Using git-credential-manager-core:"
        # Run with timeout to prevent hanging
        timeout 10 git-credential-manager-core --version || log "⚠️ Failed to get git-credential-manager-core version."
    else
        log "⚠️ Git Credential Manager not found in PATH. Installation may have failed."
        log "This is not critical - continuing with setup..."
    fi

    # Configure Git to use a simple credential store as fallback
    log "Configuring Git to use a simple credential store as fallback..."
    git config --global credential.helper 'cache --timeout=3600' || log "⚠️ Failed to configure Git credential cache."

    # Verify the step - use false for exit_on_error to continue even if it fails
    verify_command "Git Credential Manager installation" false
else
    log "Skipping Git Credential Manager installation (already completed)"
fi

# Install Docker for Ubuntu 22.04
if ! is_step_completed "Docker installation"; then
    log "Installing Docker..."
    # Check if Docker is already installed
    if command -v docker &> /dev/null; then
        log "Docker is already installed. Skipping installation."
    else
        # Install prerequisites with non-interactive options
        sudo DEBIAN_FRONTEND=noninteractive apt-get -y install apt-transport-https ca-certificates curl software-properties-common gnupg lsb-release

        # Add Docker's official GPG key
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

        # Set up the stable repository for Ubuntu 22.04 (jammy)
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

        # Update the apt package index
        sudo apt-get update

        # Install Docker Engine with non-interactive options
        sudo DEBIAN_FRONTEND=noninteractive apt-get -y install docker-ce docker-ce-cli containerd.io docker-compose-plugin

        # Start and enable Docker service
        sudo systemctl enable --now docker
    fi
    verify_command "Docker installation"

    # Verify Docker installation
    docker --version
    verify_command "Docker version check"

    # Add current user to docker group to avoid using sudo
    sudo usermod -aG docker $USER
    verify_command "Adding user to docker group"

    # Notify user about group membership
    log "⚠️ You may need to log out and log back in for the docker group membership to take effect"
    log "Alternatively, you can run: newgrp docker"
else
    log "Skipping Docker installation (already completed)"
fi

# Docker login (requires username and token to be provided)
if ! is_step_completed "Docker login"; then
    log "Setting up Docker login..."
    if [ -z "$DOCKER_USERNAME" ]; then
        log "⚠️ DOCKER_USERNAME not set. Please provide your Docker username:"
        read DOCKER_USERNAME
        # set_config "DOCKER_USERNAME" "$DOCKER_USERNAME"
    fi

    if [ -z "$DOCKER_TOKEN" ]; then
        log "⚠️ DOCKER_TOKEN not set. Please provide your Docker token:"
        read -s DOCKER_TOKEN
        # Note: We don't store the token in the config file for security reasons
    fi

    # Use a temporary file for the token to avoid it showing in process list
    DOCKER_TOKEN_FILE=$(mktemp)
    echo "$DOCKER_TOKEN" > "$DOCKER_TOKEN_FILE"
    cat "$DOCKER_TOKEN_FILE" | docker login $DOCKER_REGISTRY --username "$DOCKER_USERNAME" --password-stdin
    rm "$DOCKER_TOKEN_FILE"

    verify_command "Docker login"
else
    log "Skipping Docker login (already completed)"
fi

# Clone repository directly to home directory without creating a repo folder
if ! is_step_completed "Repository files fetching"; then
    log "Cloning repository files..."

    # Create a temporary directory for the clone
    mkdir -p "$(dirname "$TEMP_REPO_DIR")"

    # Clone the repository with depth 1 (only latest commit)
    log "Cloning repository with git clone..."

    # Check if we need GitHub credentials
    log "Checking if GitHub credentials are needed..."

    # First try to clone without credentials (in case it's a public repo)
    if timeout $GIT_TIMEOUT git ls-remote --heads "$GITHUB_REPO_URL" &>/dev/null; then
        log "Repository is accessible without credentials"
    else
        log "Repository requires authentication"

        # Ask for GitHub credentials if needed
        if [ -z "$GITHUB_USERNAME" ]; then
            log "Enter your GitHub username:"
            read GITHUB_USERNAME
        fi

        if [ -z "$GITHUB_TOKEN" ]; then
            log "GitHub requires a personal access token instead of a password."
            log "You can create one at: https://github.com/settings/tokens"
            log "Make sure it has 'repo' scope permissions."
            log "Enter your GitHub personal access token (will not be displayed):"
            read -s GITHUB_TOKEN
            echo ""  # Add a newline after the hidden input
        fi

        # Use credentials in the URL
        GITHUB_REPO_URL="https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com/AI-Brainlab/salmate-production.git"
    fi

    # Clone with the appropriate URL
    log "Cloning repository..."
    if ! timeout $GIT_TIMEOUT git clone --depth $GIT_CLONE_DEPTH "$GITHUB_REPO_URL" "$TEMP_REPO_DIR"; then
        log "❌ Failed to clone repository - check your credentials and try again"
        log "If the problem persists, you can manually clone the repository and run the script again"
        exit 1
    fi
    verify_command "Repository cloning"



    # Move all files to home directory
    log "Copying files to home directory..."

    # First, copy directories and files at the root level, including .git directory
    find "$TEMP_REPO_DIR" -mindepth 1 -maxdepth 1 \
        ! -name ".vscode" \
        ! -name ".github" \
        ! -name ".DS_Store" \
        ! -name ".venv" \
        ! -name ".build" \
        ! -name "README.md" \
        ! -name "sample" \
        ! -name "admin" \
        -exec cp -r {} "$HOME" \;

    verify_command "Copying project files"

    # Mark repository files as fetched
    echo "Repository files fetching" >> "$PROGRESS_FILE"

    # Remove any file.env and file.openai.key files that might have been copied
    log "Removing any file.env and file.openai.key files that might have been copied..."
    find "$HOME" -type f -name "file.env" -delete
    find "$HOME" -type f -name "file.openai.key" -delete

    verify_command "Repository files fetching" false
else
    log "Skipping repository cloning (already completed)"
fi

# Verify Docker is running properly
if ! is_step_completed "Docker daemon access"; then
    log "Verifying Docker is running..."
    if ! docker ps > /dev/null 2>&1; then
        log "⚠️ Docker daemon not accessible, fixing permissions..."
        sudo chmod 666 /var/run/docker.sock
        verify_command "Docker socket permission fix"

        # Verify again
        docker ps > /dev/null 2>&1
        verify_command "Docker daemon access"
    else
        log "Docker daemon is accessible"
        verify_command "Docker daemon access"
    fi
else
    log "Skipping Docker daemon verification (already completed)"
fi

# Get subdomain for configuration
SUBDOMAIN=$(get_subdomain)
log "Detected subdomain: $SUBDOMAIN"

# Function to check if secrets directory and map file exist
check_secrets_directory() {
    log "Checking secret files in $SECRETS_DIR..."

    # Check if the secrets directory exists, create it if it doesn't
    if [ ! -d "$SECRETS_DIR" ]; then
        log "Creating secrets directory: $SECRETS_DIR"
        mkdir -p "$SECRETS_DIR"
    fi

    # Check if secrets map file exists in the secrets directory
    if [ ! -f "$SECRETS_DIR/$SECRETS_MAP_FILE" ]; then
        # Copy secrets map file from the repository
        if [ -f "$(dirname "$0")/secrets/$SECRETS_MAP_FILE" ]; then
            log "Copying $SECRETS_MAP_FILE to $SECRETS_DIR"
            cp "$(dirname "$0")/secrets/$SECRETS_MAP_FILE" "$SECRETS_DIR/"
        else
            log "❌ Error: $SECRETS_MAP_FILE not found in $(dirname "$0")/secrets/"
            log "❌ Exiting script due to missing secrets map file"
            exit 1
        fi
    fi

    # Check for secrets in the admin/secrets directory
    REPO_SECRETS_DIR="$HOME/admin/secrets"

    if [ ! -d "$REPO_SECRETS_DIR" ]; then
        log "❌ Expected secrets directory not found at $REPO_SECRETS_DIR"
        log "❌ Repository structure may be different than expected"
        log "❌ Exiting script due to missing secrets directory"
        exit 1
    else
        log "Found secrets directory at $REPO_SECRETS_DIR"
    fi

    # Check if we can read files in the secrets directory
    SECRET_FILES_COUNT=$(find "$REPO_SECRETS_DIR" -type f -name "*.env" -o -name "*.key" | wc -l)
    if [ "$SECRET_FILES_COUNT" -gt 0 ]; then
        log "Found $SECRET_FILES_COUNT secret files in $REPO_SECRETS_DIR"
    else
        log "❌ No secret files found in $REPO_SECRETS_DIR"
        log "❌ Repository structure may be different than expected"
        log "❌ Exiting script due to missing secret files"
        exit 1
    fi

    # Check if jq is installed (for JSON parsing)
    if ! command -v jq &> /dev/null; then
        log "Installing jq for JSON parsing..."
        sudo DEBIAN_FRONTEND=noninteractive apt-get -y install jq
    fi

    return 0
}

# Deploy secret files according to paths in secrets/secrets-map.json file
if ! is_step_completed "Secret files deployment"; then
    log "Deploying secret files from secrets directory..."

    # Get subdomain for configuration if not already set
    if [ -z "$SUBDOMAIN" ]; then
        SUBDOMAIN=$(get_subdomain)
        log "Detected subdomain: $SUBDOMAIN"
    fi

    # Define the secrets map file path
    SECRETS_MAP="$SECRETS_DIR/$SECRETS_MAP_FILE"

    # Check if secrets directory and map file exist
    check_secrets_directory

    log "Using secrets directory: $SECRETS_DIR"

    # Parse the JSON file and deploy the secret files
    # Get the number of secrets
    SECRET_COUNT=$(jq '.secrets | length' "$SECRETS_MAP")
    log "Found $SECRET_COUNT secret files to deploy"

    if [ "$SECRET_COUNT" -eq 0 ]; then
        log "❌ Error: No secret files found in $SECRETS_MAP"
        log "❌ Exiting script due to missing secret files"
        exit 1
    fi

    # Track how many files were successfully deployed
    DEPLOYED_FILES=0
    TOTAL_DEPLOYMENTS=0

    # Loop through each secret
    for ((i=0; i<$SECRET_COUNT; i++)); do
        # Get the filename
        filename=$(jq -r ".secrets[$i].filename" "$SECRETS_MAP")
        source_file="$REPO_SECRETS_DIR/$filename"

        # Exit if the source file doesn't exist
        if [ ! -f "$source_file" ]; then
            log "❌ Error: Source file not found: $source_file"
            log "❌ Exiting script due to missing secret file"
            exit 1
        fi

        # Get the number of destinations for this file
        DEST_COUNT=$(jq ".secrets[$i].destinations | length" "$SECRETS_MAP")
        TOTAL_DEPLOYMENTS=$((TOTAL_DEPLOYMENTS + DEST_COUNT))

        # Loop through each destination
        for ((j=0; j<$DEST_COUNT; j++)); do
            # Get the destination path
            dest_path=$(jq -r ".secrets[$i].destinations[$j]" "$SECRETS_MAP")
            # Expand $HOME if present
            dest_path="${dest_path/\$HOME/$HOME}"

            # Create the destination directory if it doesn't exist
            dest_dir=$(dirname "$dest_path")
            if [ ! -d "$dest_dir" ]; then
                log "Creating directory: $dest_dir"
                mkdir -p "$dest_dir"
            fi

            # Create a temporary file with ${SUBDOMAIN} placeholders replaced
            temp_file=$(mktemp)

            # Replace ${SUBDOMAIN} with actual value in the file content
            log "Replacing {SUBDOMAIN} placeholders in $filename with actual value: $SUBDOMAIN"
            sed "s/\${SUBDOMAIN}/$SUBDOMAIN/g" "$source_file" > "$temp_file"

            # Copy the processed file to destination
            log "Copying processed $filename to $dest_path"
            if cp "$temp_file" "$dest_path"; then
                # Set proper permissions
                chmod $DEFAULT_FILE_PERMISSIONS "$dest_path"
                DEPLOYED_FILES=$((DEPLOYED_FILES + 1))
            else
                log "❌ Error: Failed to copy $filename to $dest_path"
            fi

            # Clean up temporary file
            rm -f "$temp_file"
        done
    done

    # Check if we deployed at least some files
    if [ "$DEPLOYED_FILES" -eq 0 ]; then
        log "❌ Error: Failed to deploy any secret files"
        log "❌ Exiting script due to failure to deploy secrets"
        exit 1
    elif [ "$DEPLOYED_FILES" -lt "$TOTAL_DEPLOYMENTS" ]; then
        log "⚠️ Warning: Only deployed $DEPLOYED_FILES out of $TOTAL_DEPLOYMENTS secret files"
        log "⚠️ Some features may not work correctly"
    else
        log "Successfully deployed all $DEPLOYED_FILES secret files"
    fi

    log "Secret files deployment completed"

    verify_command "Secret files deployment" false

else
    log "Skipping secret files deployment (already completed)"
fi

# Update Traefik configuration with subdomain
if ! is_step_completed "Traefik configuration update"; then
    log "Updating Traefik configuration with subdomain..."

    # Get subdomain for configuration if not already set
    if [ -z "$SUBDOMAIN" ]; then
        SUBDOMAIN=$(get_subdomain)
        log "Detected subdomain: $SUBDOMAIN"
    fi

    # Path to Traefik configuration file
    TRAEFIK_CONFIG="$TRAEFIK_DIR/config/traefik.yaml"

    if [ -f "$TRAEFIK_CONFIG" ]; then
        log "Replacing ${SUBDOMAIN} placeholder in $TRAEFIK_CONFIG"

        # Create a temporary file with ${SUBDOMAIN} placeholders replaced
        temp_file=$(mktemp)

        # Replace ${SUBDOMAIN} with actual value in the file content
        sed "s/\${SUBDOMAIN}/$SUBDOMAIN/g" "$TRAEFIK_CONFIG" > "$temp_file"

        # Copy the processed file back to the original location
        if cp "$temp_file" "$TRAEFIK_CONFIG"; then
            log "Successfully updated Traefik configuration with subdomain: $SUBDOMAIN"
        else
            log "❌ Error: Failed to update Traefik configuration"
        fi

        # Clean up temporary file
        rm -f "$temp_file"
    else
        log "❌ Error: Traefik configuration file not found at $TRAEFIK_CONFIG"
    fi

    verify_command "Traefik configuration update" false
else
    log "Skipping Traefik configuration update (already completed)"
fi

# Update Alertmanager configuration with subdomain
if ! is_step_completed "Alertmanager configuration update"; then
    log "Updating Alertmanager configuration with subdomain..."

    # Get subdomain for configuration if not already set
    if [ -z "$SUBDOMAIN" ]; then
        SUBDOMAIN=$(get_subdomain)
        log "Detected subdomain: $SUBDOMAIN"
    fi

    # Path to Alertmanager configuration file
    ALERTMANAGER_CONFIG="$MONITORING_DIR/alertmanager/alertmanager.yml"

    if [ -f "$ALERTMANAGER_CONFIG" ]; then
        log "Replacing ${SUBDOMAIN} placeholder in $ALERTMANAGER_CONFIG"

        # Create a temporary file with ${SUBDOMAIN} placeholders replaced
        temp_file=$(mktemp)

        # Replace ${SUBDOMAIN} with actual value in the file content
        sed "s/\${SUBDOMAIN}/$SUBDOMAIN/g" "$ALERTMANAGER_CONFIG" > "$temp_file"

        # Copy the processed file back to the original location
        if cp "$temp_file" "$ALERTMANAGER_CONFIG"; then
            log "Successfully updated Alertmanager configuration with subdomain: $SUBDOMAIN"
        else
            log "❌ Error: Failed to update Alertmanager configuration"
        fi

        # Clean up temporary file
        rm -f "$temp_file"
    else
        log "❌ Error: Alertmanager configuration file not found at $ALERTMANAGER_CONFIG"
    fi

    verify_command "Alertmanager configuration update" false
else
    log "Skipping Alertmanager configuration update (already completed)"
fi

# Bring up Docker containers
# traefik
if ! is_step_completed "Traefik container startup"; then
    log "Starting Traefik container..."
    pushd "$TRAEFIK_DIR" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "Traefik container startup"
else
    log "Skipping Traefik container startup (already completed)"
fi

# Bring up Postgres containers
if ! is_step_completed "Postgres container startup"; then
    log "Starting Postgres containers..."
    pushd "$POSTGRES_DIR" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "Postgres container startup"
else
    log "Skipping Postgres container startup (already completed)"
fi

# Bring up Redis containers
if ! is_step_completed "Redis container startup"; then
    log "Starting Redis containers..."
    pushd "$REDIS_DIR" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "Redis container startup"
else
    log "Skipping Redis container startup (already completed)"
fi

# Bring up llm/postgres-db containers
if ! is_step_completed "llm/postgres-db container startup"; then
    log "Starting llm/postgres-db containers..."
    pushd "$LLM_DIR/postgres-db" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "llm/postgres-db container startup"
else
    log "Skipping llm/postgres-db container startup (already completed)"
fi

# Bring up llm/postgres-state containers
if ! is_step_completed "llm/postgres-state container startup"; then
    log "Starting llm/postgres-state containers..."
    pushd "$LLM_DIR/postgres-state" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "llm/postgres-state container startup"
else
    log "Skipping llm/postgres-state container startup (already completed)"
fi

# Bring up llm/tool containers
if ! is_step_completed "llm/tool container startup"; then
    log "Starting llm/tool containers with tag $TAG..."
    pushd "$LLM_DIR/tool" > /dev/null

    # Extract image name and validate tag
    extract_image_name "$LLM_DIR/tool" "llm/tool" > /dev/null

    # Set the tag environment variable
    export tag=$TAG
    echo ${tag} > tag
    docker compose up -d
    popd > /dev/null
    verify_command "llm/tool container startup"
else
    log "Skipping llm/tool container startup (already completed)"
fi

# Bring up llm/information containers
if ! is_step_completed "llm/information container startup"; then
    log "Starting llm/information containers with tag $TAG..."
    pushd "$LLM_DIR/information" > /dev/null

    # Extract image name and validate tag
    extract_image_name "$LLM_DIR/information" "llm/information" > /dev/null

    # Set the tag environment variable
    export tag=$TAG
    echo ${tag} > tag
    docker compose up -d
    popd > /dev/null
    verify_command "llm/information container startup"
else
    log "Skipping llm/information container startup (already completed)"
fi

# Bring up llm/intent containers
if ! is_step_completed "llm/intent container startup"; then
    log "Starting llm/intent containers with tag $TAG..."
    pushd "$LLM_DIR/intent" > /dev/null

    # Extract image name and validate tag
    extract_image_name "$LLM_DIR/intent" "llm/intent" > /dev/null

    # Set the tag environment variable
    export tag=$TAG
    echo ${tag} > tag
    docker compose up -d
    popd > /dev/null
    verify_command "llm/intent container startup"
else
    log "Skipping llm/intent container startup (already completed)"
fi

# Bring up llm/langgraph containers
if ! is_step_completed "llm/langgraph container startup"; then
    log "Starting llm/langgraph containers with tag $TAG..."
    pushd "$LLM_DIR/langgraph" > /dev/null

    # Extract image name and validate tag
    extract_image_name "$LLM_DIR/langgraph" "llm/langgraph" > /dev/null

    # Set the tag environment variable
    export tag=$TAG
    echo ${tag} > tag
    docker compose up -d
    popd > /dev/null
    verify_command "llm/langgraph container startup"
else
    log "Skipping llm/langgraph container startup (already completed)"
fi

# Bring up backend containers
if ! is_step_completed "Backend container startup"; then
    log "Starting Backend containers with tag $TAG..."
    pushd "$BACKEND_DIR" > /dev/null

    # Extract image name and validate tag
    extract_image_name "$BACKEND_DIR" "backend" > /dev/null

    # Set the tag environment variable
    export tag=$TAG
    echo ${tag} > tag

    # Start the containers in detached mode
    docker compose up -d
    popd > /dev/null
    verify_command "Backend containers startup"

    # Wait for containers to be fully up
    log "Waiting for backend containers to be fully up..."
    sleep $BACKEND_INIT_WAIT

    # Get the backend container name
    BACKEND_CONTAINER=$(docker ps --filter "name=backend" --format "{{.Names}}" | grep -i backend | head -n 1)

    if [ -n "$BACKEND_CONTAINER" ]; then
        log "Found backend container: $BACKEND_CONTAINER"

        # Setup the database
        # log "Setting up the database..."
        # docker exec -i $BACKEND_CONTAINER /bin/bash -c "poe makemigrations"
        # verify_command "Database makemigrations" false

        # docker exec -i $BACKEND_CONTAINER /bin/bash -c "poe migrate"
        # verify_command "Database migrate" false

        # docker exec -i $BACKEND_CONTAINER /bin/bash -c "poe initdb"
        # verify_command "Database initialization" false

        log "Backend setup completed successfully"
    else
        log "⚠️ Backend container not found. Database setup skipped."
        log "You may need to manually run: docker exec -it <container-name> /bin/bash"
        log "Then run: poe makemigrations && poe migrate && poe initdb"
    fi
else
    log "Skipping Backend container startup (already completed)"
fi

# Bring up frontend containers
if ! is_step_completed "Frontend container startup"; then
    log "Starting Frontend containers..."
    pushd "$FRONTEND_DIR" > /dev/null
    docker compose up -d
    popd > /dev/null
    verify_command "Frontend container startup"
else
    log "Skipping Frontend container startup (already completed)"
fi

# Bring up monitoring containers
if ! is_step_completed "Monitoring container startup"; then
    log "Starting Monitoring containers..."
    pushd "$MONITORING_DIR" > /dev/null

    # Fix permissions for bind mount directories
    # mkdir -p "$MONITORING_DIR/dev-grafana-data"
    mkdir -p "$MONITORING_DIR/dev-prom-data"
    # sudo chmod -R 777 "$MONITORING_DIR/dev-grafana-data"
    sudo chmod -R 777 "$MONITORING_DIR/dev-prom-data"
    # sudo chmod -R 777 "$MONITORING_DIR/grafana"
    sudo chmod -R 777 "$MONITORING_DIR/prometheus"

    docker compose up -d
    popd > /dev/null
    verify_command "Monitoring container startup"
else
    log "Skipping Monitoring container startup (already completed)"
fi

# Setup ctop alias
log "Setting up ctop alias..."
echo "alias ctop='docker run --rm -ti --name ctop -v /var/run/docker.sock:/var/run/docker.sock quay.io/vektorlab/ctop:latest'" > "$HOME/.bash_aliases"
source "$HOME/.bash_aliases"
verify_command "Setup ctop alias" false

# Perform final cleanup tasks
log "Performing final cleanup tasks..."

# Clean up all temporary directories
if ! is_step_completed "Cleanup temporary directories"; then
    log "Cleaning up temporary directories..."

    # Remove temporary repository directory
    if [ -d "$TEMP_REPO_DIR" ]; then
        rm -rf "$TEMP_REPO_DIR"
    fi

    # Remove temp_git directory
    if [ -d "$HOME/temp_git" ]; then
        rm -rf "$HOME/temp_git"
    fi

    verify_command "Cleanup temporary directories" false
else
    log "Skipping temporary directories cleanup (already completed)"
fi

# Final check to ensure git tracking is removed
if ! is_step_completed "Git tracking removal"; then
    log "Final check: Removing git tracking from the home directory..."
    if [ -d "$HOME/.git" ]; then
        log "Removing .git directory to make this folder not git tracked anymore..."
        rm -rf "$HOME/.git"
        verify_command "Git tracking removal" false
    else
        log "No .git directory found, folder is already not git tracked"
        echo "Git tracking removal" >> "$PROGRESS_FILE"
    fi
else
    log "Git tracking has already been removed"
fi

# Clean up progress file if everything completed successfully
if [ -f "$PROGRESS_FILE" ]; then
    TOTAL_STEPS=$(wc -l < "$PROGRESS_FILE")
    log "$(echo -e "\e[32m✅ Post-deployment setup completed successfully with $TOTAL_STEPS steps!\e[0m")"

else
    log "$(echo -e "\e[32m✅ Post-deployment setup completed successfully!\e[0m")"
fi

# Add completion message to log file
echo "=== Setup completed on $(date '+%Y-%m-%d %H:%M:%S') ===" >> "$LOG_FILE"

# Remind user about the log file
log "Complete setup log is available at: $LOG_FILE"
