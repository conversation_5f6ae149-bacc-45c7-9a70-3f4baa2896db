#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration variables - for reference only, not used in cleanup
TAG="v1.0.0"  # This matches the tag used in initial-setup.sh
SECRETS_DIR="$HOME/admin/secrets"  # Location of secrets directory

# Function to log messages with timestamps
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to verify command execution
verify_command() {
    local step_name="$1"
    local exit_on_error="${2:-true}"  # Default to true if not provided

    if [ $? -eq 0 ]; then
        log "$(echo -e "\e[32m✅ $step_name completed successfully\e[0m")"
        return 0
    else
        if [ "$exit_on_error" = "true" ]; then
            log "❌ $step_name failed - exiting script"
            exit 1
        else
            log "⚠️ $step_name failed - continuing anyway"
            return 1
        fi
    fi
}

# Display warning message
echo "⚠️  WARNING: This script will remove all changes made by initial-setup.sh, including:"
echo "   - Configuration files"
echo "   - Deployed secret files"
echo "   - Environment files"
echo "   - Git tracking and related files"
echo "   - Git and Docker configurations"
echo "   - All running Docker containers"
echo "   - Cloned repository files"
echo "   - Temporary git repositories"
echo ""
echo "This is intended for testing purposes only."
echo ""
read -p "Are you sure you want to continue? (y/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log "Cleanup cancelled by user."
    exit 0
fi

log "Starting cleanup of changes made by initial-setup.sh..."

# 1. Remove configuration files
log "Removing configuration files..."
# Function to safely remove a file with sudo if needed (if not already defined)
if [ -z "$(declare -f safe_remove_file)" ]; then
    safe_remove_file() {
        local file="$1"
        if [ -f "$file" ]; then
            log "Removing $file"
            rm -f "$file" 2>/dev/null || {
                log "⚠️ Permission denied, trying with sudo..."
                # Ask for sudo password only once per session
                if [ -z "$SUDO_PASSWORD_ASKED" ]; then
                    log "Some files require elevated permissions to remove."
                    log "You may be prompted for your sudo password."
                    SUDO_PASSWORD_ASKED=1
                fi
                sudo rm -f "$file" || {
                    log "⚠️ Failed to remove $file even with sudo, continuing anyway..."
                }
            }
        fi
    }
fi

# Remove configuration files
safe_remove_file "$HOME/.salmate_config"
safe_remove_file "$HOME/.salmate_progress"

log "✅ Configuration files removal attempt completed"

# 2. Remove deployed secret files based on secrets-map.json
log "Removing deployed secret files..."

# Define the secrets map file path
SECRETS_MAP="$SECRETS_DIR/secrets-map.json"

log "Using secrets directory: $SECRETS_DIR"

if [ -f "$SECRETS_MAP" ]; then
    # Check if jq is installed
    if command -v jq &> /dev/null; then
        # Parse the JSON file to find deployed files
        SECRET_COUNT=$(jq '.secrets | length' "$SECRETS_MAP")
        log "Found $SECRET_COUNT secret file entries to clean up"

        # Loop through each secret
        for ((i=0; i<$SECRET_COUNT; i++)); do
            # Get the destinations for this file
            DEST_COUNT=$(jq ".secrets[$i].destinations | length" "$SECRETS_MAP")

            # Loop through each destination
            for ((j=0; j<$DEST_COUNT; j++)); do
                # Get the destination path
                dest_path=$(jq -r ".secrets[$i].destinations[$j]" "$SECRETS_MAP")
                # Expand $HOME if present
                dest_path="${dest_path/\$HOME/$HOME}"

                # Remove the file if it exists
                if [ -f "$dest_path" ]; then
                    log "Removing $dest_path"
                    rm -f "$dest_path" 2>/dev/null || {
                        log "⚠️ Permission denied, trying with sudo..."
                        # Ask for sudo password only once per session
                        if [ -z "$SUDO_PASSWORD_ASKED" ]; then
                            log "Some files require elevated permissions to remove."
                            log "You may be prompted for your sudo password."
                            SUDO_PASSWORD_ASKED=1
                        fi
                        sudo rm -f "$dest_path" || {
                            log "⚠️ Failed to remove $dest_path even with sudo, continuing anyway..."
                        }
                    }
                fi

                # Check if the parent directory is empty and remove it if it is
                dest_dir=$(dirname "$dest_path")
                if [ -d "$dest_dir" ] && [ -z "$(ls -A "$dest_dir" 2>/dev/null)" ]; then
                    log "Removing empty directory: $dest_dir"
                    rmdir "$dest_dir" 2>/dev/null || {
                        log "⚠️ Failed to remove directory, trying with sudo..."
                        sudo rmdir "$dest_dir" || {
                            log "⚠️ Failed to remove directory even with sudo, continuing anyway..."
                        }
                    }
                fi
            done
        done

        log "✅ Secret files removal attempt completed"
    else
        log "⚠️ jq is not installed. Cannot parse secrets-map.json."
    fi
else
    log "⚠️ secrets-map.json not found. Skipping secret files cleanup."
fi

# 3. Remove .env files and other files that might have been modified with subdomain replacements
log "Removing .env files and other modified files..."
ENV_DIRS=(
    "$HOME/_traefik"
    "$HOME/backend"
    "$HOME/llm/intent"
    "$HOME/llm/default"
    "$HOME/llm/information"
    "$HOME/llm/langgraph"
    "$HOME/llm/tool"
    "$HOME/llm/postgres-db"
    "$HOME/llm/postgres-state"
    "$HOME/postgres"
    "$HOME/redis"
    "$HOME/monitoring"
    "$HOME/mongodb"
    "$HOME/frontend"
)

# Function to safely remove a file with sudo if needed
safe_remove_file() {
    local file="$1"
    if [ -f "$file" ]; then
        log "Removing $file"
        rm -f "$file" 2>/dev/null || {
            log "⚠️ Permission denied, trying with sudo..."
            # Ask for sudo password only once per session
            if [ -z "$SUDO_PASSWORD_ASKED" ]; then
                log "Some files require elevated permissions to remove."
                log "You may be prompted for your sudo password."
                SUDO_PASSWORD_ASKED=1
            fi
            sudo rm -f "$file" || {
                log "⚠️ Failed to remove $file even with sudo, continuing anyway..."
            }
        }
    fi
}

for dir in "${ENV_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        # Remove .env file
        safe_remove_file "$dir/.env"

        # Remove .openai.key file
        safe_remove_file "$dir/.openai.key"

        # Check if the directory is empty and remove it if it is
        if [ -z "$(ls -A "$dir" 2>/dev/null)" ]; then
            log "Removing empty directory: $dir"
            rmdir "$dir" 2>/dev/null || {
                log "⚠️ Failed to remove directory, trying with sudo..."
                sudo rmdir "$dir" || {
                    log "⚠️ Failed to remove directory even with sudo, continuing anyway..."
                }
            }
        fi
    fi
done

log "✅ Environment files removal attempt completed"

# 4. Reset Git and GitHub configuration
log "Resetting Git and GitHub configuration..."

# Reset Git credential manager configuration
if git config --global --get credential.helper &> /dev/null; then
    log "Unsetting Git credential helper..."
    git config --global --unset credential.helper
    verify_command "Git credential helper reset" false
fi

# Remove GitHub credentials from environment
if [ -n "$GITHUB_USERNAME" ]; then
    log "Unsetting GITHUB_USERNAME environment variable..."
    unset GITHUB_USERNAME
fi

if [ -n "$GITHUB_TOKEN" ]; then
    log "Unsetting GITHUB_TOKEN environment variable..."
    unset GITHUB_TOKEN
fi



# Remove Docker credentials from environment
if [ -n "$DOCKER_USERNAME" ]; then
    log "Unsetting DOCKER_USERNAME environment variable..."
    unset DOCKER_USERNAME
fi

if [ -n "$DOCKER_TOKEN" ]; then
    log "Unsetting DOCKER_TOKEN environment variable..."
    unset DOCKER_TOKEN
fi

# Log out from Docker if logged in
if command -v docker &> /dev/null; then
    log "Logging out from Docker..."
    docker logout || {
        log "⚠️ Failed to log out from Docker, but continuing anyway..."
    }
    log "✅ Docker logout attempt completed"
fi

# 5. Stop and remove all Docker containers, networks, and volumes
log "Stopping all Docker services..."

# Function to check if a Docker network exists
docker_network_exists() {
    local network_name="$1"
    docker network ls --format "{{.Name}}" | grep -q "^$network_name$"
    return $?
}

# Remove specific Docker networks that might have been created
NETWORKS_TO_REMOVE=(
    "traefik_default"
    "backend_default"
    "llm_default"
    "postgres_default"
    "redis_default"
    "mongodb_default"
)

# Function to check if a Docker volume exists
docker_volume_exists() {
    local volume_name="$1"
    docker volume ls --format "{{.Name}}" | grep -q "^$volume_name$"
    return $?
}

# Remove specific Docker volumes that might have been created
VOLUMES_TO_REMOVE=(
    "backend_postgres-data"
    "postgres_data"
    "mongodb_data"
    "redis_data"
    "llm_postgres-data"
    "llm_tool-data"
)

# Remove specific networks
for network in "${NETWORKS_TO_REMOVE[@]}"; do
    if docker_network_exists "$network"; then
        log "Removing Docker network: $network"
        docker network rm "$network" || {
            log "⚠️ Failed to remove Docker network $network, but continuing anyway..."
        }
    fi
done

# Remove specific volumes
for volume in "${VOLUMES_TO_REMOVE[@]}"; do
    if docker_volume_exists "$volume"; then
        log "Removing Docker volume: $volume"
        docker volume rm "$volume" || {
            log "⚠️ Failed to remove Docker volume $volume, but continuing anyway..."
        }
    fi
done
if command -v docker &> /dev/null; then
    # Check for Docker Compose files and stop services
    COMPOSE_DIRS=(
        "$HOME/_traefik"
        "$HOME/backend"
        "$HOME/llm/intent"
        "$HOME/llm/default"
        "$HOME/llm/information"
        "$HOME/llm/langgraph"
        "$HOME/llm/tool"
        "$HOME/llm/postgres-db"
        "$HOME/llm/postgres-state"
        "$HOME/postgres"
        "$HOME/redis"
        "$HOME/monitoring"
        "$HOME/mongodb"
        "$HOME/Salmate"
        "$HOME/Salmate_DataCrawler"
        "$HOME/salmate-frontend"
        "$HOME/frontend"
    )

    # Special handling for containers that use the tag variable
    TAG_USING_DIRS=(
        "$HOME/backend"
        "$HOME/llm/tool"
        "$HOME/llm/information"
        "$HOME/llm/intent"
        "$HOME/llm/langgraph"
        "$HOME/llm/default"
    )

    # Special handling for backend container to reset database
    if [ -f "$HOME/backend/docker-compose.yml" ] || [ -f "$HOME/backend/docker-compose.yaml" ]; then
        log "Checking for running backend container to reset database..."
        BACKEND_CONTAINER=$(docker ps --filter "name=backend" --format "{{.Names}}" | grep -i backend | head -n 1)

        if [ -n "$BACKEND_CONTAINER" ]; then
            log "Found backend container: $BACKEND_CONTAINER. Attempting to reset database..."
            # This is a safer approach than trying to drop the database
            docker exec -i $BACKEND_CONTAINER /bin/bash -c "poe flush" || {
                log "⚠️ Failed to flush database, but continuing anyway..."
            }
        fi
    fi

    for dir in "${TAG_USING_DIRS[@]}"; do
        if [ -f "$dir/docker-compose.yml" ] || [ -f "$dir/docker-compose.yaml" ]; then
            log "Found Docker Compose file in $dir that might use tag variable, stopping services..."
            cd "$dir"

            # Unset any tag variable that might be set
            unset tag

            if command -v docker-compose &> /dev/null; then
                # Use || true to continue even if the command fails
                docker-compose down -v || {
                    log "⚠️ Failed to stop Docker Compose services in $dir, but continuing anyway..."
                }
            elif docker compose version &> /dev/null; then
                # Use || true to continue even if the command fails
                docker compose down -v || {
                    log "⚠️ Failed to stop Docker Compose services in $dir, but continuing anyway..."
                }
            fi
            cd - > /dev/null
        fi
    done

    # Handle other Docker Compose services
    for dir in "${COMPOSE_DIRS[@]}"; do
        # Skip directories we've already handled
        skip=false
        for tag_dir in "${TAG_USING_DIRS[@]}"; do
            if [ "$dir" = "$tag_dir" ]; then
                skip=true
                break
            fi
        done

        if [ "$skip" = true ]; then
            continue
        fi

        if [ -f "$dir/docker-compose.yml" ] || [ -f "$dir/docker-compose.yaml" ]; then
            log "Found Docker Compose file in $dir, stopping services..."
            cd "$dir"
            if command -v docker-compose &> /dev/null; then
                # Use || true to continue even if the command fails
                docker-compose down -v || {
                    log "⚠️ Failed to stop Docker Compose services in $dir, but continuing anyway..."
                }
            elif docker compose version &> /dev/null; then
                # Use || true to continue even if the command fails
                docker compose down -v || {
                    log "⚠️ Failed to stop Docker Compose services in $dir, but continuing anyway..."
                }
            fi
            # Don't verify the command since we're handling failures inline
            cd - > /dev/null
        fi
    done

    # Stop all running containers
    if [ "$(docker ps -q)" ]; then
        log "Stopping all running Docker containers..."
        docker stop $(docker ps -q) || {
            log "⚠️ Failed to stop some Docker containers, but continuing anyway..."
        }
        log "✅ Docker containers stop attempt completed"
    else
        log "No running Docker containers found."
    fi

    # Remove all containers
    if [ "$(docker ps -a -q)" ]; then
        log "Removing all Docker containers..."
        docker rm $(docker ps -a -q) || {
            log "⚠️ Failed to remove some Docker containers, but continuing anyway..."
            log "Trying to force remove containers..."
            docker rm -f $(docker ps -a -q) || {
                log "⚠️ Failed to force remove some containers, but continuing anyway..."
            }
        }
        log "✅ Docker containers removal attempt completed"
    else
        log "No Docker containers to remove."
    fi

    # Remove all unused networks
    log "Removing unused Docker networks..."
    docker network prune -f || {
        log "⚠️ Failed to prune Docker networks, but continuing anyway..."
    }
    log "✅ Docker networks prune attempt completed"

    # Remove all unused volumes
    log "Removing unused Docker volumes..."
    docker volume prune -f || {
        log "⚠️ Failed to prune Docker volumes, but continuing anyway..."
    }
    log "✅ Docker volumes prune attempt completed"

    # Remove unused images
    log "Removing unused Docker images..."
    docker image prune -f || {
        log "⚠️ Failed to prune Docker images, but continuing anyway..."
    }
    log "✅ Docker images prune attempt completed"

    # Check for any remaining containers with specific names
    CONTAINER_PATTERNS=(
        "backend"
        "traefik"
        "postgres"
        "redis"
        "mongodb"
        "llm"
        "salmate"
    )

    for pattern in "${CONTAINER_PATTERNS[@]}"; do
        CONTAINERS=$(docker ps -a --filter "name=$pattern" --format "{{.Names}}")
        if [ -n "$CONTAINERS" ]; then
            log "Found containers matching pattern '$pattern', attempting to remove..."
            for container in $CONTAINERS; do
                log "Stopping and removing container: $container"
                docker stop "$container" || true
                docker rm "$container" || {
                    log "⚠️ Failed to remove container $container, trying with force..."
                    docker rm -f "$container" || {
                        log "⚠️ Failed to force remove container $container, but continuing anyway..."
                    }
                }
            done
        fi
    done

    # Full system prune (optional)
    log "Performing full Docker system prune..."
    docker system prune -f || {
        log "⚠️ Failed to perform full Docker system prune, but continuing anyway..."
    }
    log "✅ Docker system prune attempt completed"

    # Delete all images
    log "Removing all Docker images..."
    docker rmi $(docker images -q) -f || {
        log "⚠️ Failed to remove all Docker images, but continuing anyway..."
    }
    log "✅ Docker images removal attempt completed"
else
    log "Docker is not installed. Skipping Docker cleanup."
fi

# 6. Remove cloned repository files
log "Removing cloned repository files..."

# Function to safely remove directories with sudo if needed
safe_remove() {
    local dir="$1"
    if [ -e "$dir" ]; then
        log "Removing $dir..."
        # First try without sudo
        rm -rf "$dir" 2>/dev/null || {
            log "⚠️ Permission denied, trying with sudo..."
            # Ask for sudo password only once per session
            if [ -z "$SUDO_PASSWORD_ASKED" ]; then
                log "Some files require elevated permissions to remove."
                log "You may be prompted for your sudo password."
                SUDO_PASSWORD_ASKED=1
            fi
            sudo rm -rf "$dir" || {
                log "⚠️ Failed to remove $dir even with sudo, continuing anyway..."
            }
        }
    fi
}

# Directories to remove
DIRS_TO_REMOVE=(
    "$HOME/temp_git"
    "$HOME/Salmate"
    "$HOME/Salmate_DataCrawler"
    "$HOME/_traefik"
    "$HOME/backend"
    "$HOME/frontend"
    "$HOME/llm"
    "$HOME/mongodb"
    "$HOME/monitoring"
    "$HOME/postgres"
    "$HOME/redis"
    "$HOME/salmate-frontend"
    "$HOME/sample"
)

# Remove each directory
for dir in "${DIRS_TO_REMOVE[@]}"; do
    safe_remove "$dir"
done

# Remove specific files
if [ -f "$HOME/README.md" ]; then
    rm -f "$HOME/README.md" || {
        log "⚠️ Failed to remove README.md, trying with sudo..."
        sudo rm -f "$HOME/README.md" || {
            log "⚠️ Failed to remove README.md even with sudo, continuing anyway..."
        }
    }
fi

# Remove tag files that might have been created
TAG_DIRS=(
    "$HOME/backend"
    "$HOME/llm/tool"
    "$HOME/llm/information"
    "$HOME/llm/intent"
    "$HOME/llm/langgraph"
    "$HOME/llm/default"
)

for dir in "${TAG_DIRS[@]}"; do
    if [ -f "$dir/tag" ]; then
        log "Removing tag file in $dir"
        rm -f "$dir/tag" 2>/dev/null || {
            log "⚠️ Failed to remove tag file, trying with sudo..."
            sudo rm -f "$dir/tag" || {
                log "⚠️ Failed to remove tag file even with sudo, continuing anyway..."
            }
        }
    fi
done

log "✅ Repository files removal attempt completed"

# 7. Clean up git tracking from all directories
log "Cleaning up git tracking from all directories..."

# Function to remove git tracking from a directory
remove_git_tracking() {
    local dir="$1"
    if [ -d "$dir/.git" ]; then
        log "Removing git tracking from $dir"
        rm -rf "$dir/.git" 2>/dev/null || {
            log "⚠️ Failed to remove .git directory from $dir, trying with sudo..."
            sudo rm -rf "$dir/.git" || {
                log "⚠️ Failed to remove .git directory from $dir even with sudo, continuing anyway..."
            }
        }

        # Remove git-related files
        rm -f "$dir/.gitignore" 2>/dev/null
        rm -f "$dir/.gitattributes" 2>/dev/null
        rm -f "$dir/.gitmodules" 2>/dev/null

        return 0
    else
        return 1
    fi
}

# First check the home directory
if remove_git_tracking "$HOME"; then
    log "✅ Successfully removed git tracking from home directory"
else
    log "No git tracking found in home directory"
fi

# Check for any remaining .git directories in subdirectories
log "Checking for git tracking in subdirectories..."
FOUND_GIT_DIRS=$(find "$HOME" -name ".git" -type d 2>/dev/null | grep -v "node_modules" || echo "")

if [ -n "$FOUND_GIT_DIRS" ]; then
    log "Found git directories in subdirectories, removing..."
    for git_dir in $FOUND_GIT_DIRS; do
        parent_dir=$(dirname "$git_dir")
        log "Removing git tracking from $parent_dir"
        rm -rf "$git_dir" 2>/dev/null || {
            log "⚠️ Failed to remove $git_dir, trying with sudo..."
            sudo rm -rf "$git_dir" || {
                log "⚠️ Failed to remove $git_dir even with sudo, continuing anyway..."
            }
        }

        # Remove git-related files
        rm -f "$parent_dir/.gitignore" 2>/dev/null
        rm -f "$parent_dir/.gitattributes" 2>/dev/null
        rm -f "$parent_dir/.gitmodules" 2>/dev/null
    done
    log "✅ Git tracking removal from subdirectories completed"
else
    log "No git tracking found in subdirectories"
fi

# Clean up any temporary git repositories that might have been created
TEMP_GIT_DIRS=(
    "$HOME/temp_git"
)

for dir in "${TEMP_GIT_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        log "Removing temporary git repository: $dir"
        rm -rf "$dir" 2>/dev/null || {
            log "⚠️ Failed to remove $dir, trying with sudo..."
            sudo rm -rf "$dir" || {
                log "⚠️ Failed to remove $dir even with sudo, continuing anyway..."
            }
        }
    fi
done

log "✅ Git tracking cleanup completed"

# 8. Clean up environment variables set by setup.sh
log "Cleaning up environment variables..."
ENV_VARS=(
    "TRAEFIK_HOST"
    "BACKEND_HOST"
    "INTENT_HOST"
    "DEBIAN_FRONTEND"
    "GCM_VERSION"
    "GCM_DEB_NAME"
    "SUBDOMAIN"
    "GITHUB_REPO_URL"
    "SECRETS_MAP"
    "SECRET_COUNT"
    "DEST_COUNT"
    "tag"
    "BACKEND_CONTAINER"
    "COMPLETED_STEPS"
    "SUDO_PASSWORD_ASKED"
    # Git-related variables
    "GIT_CLONE_DEPTH"
    "GIT_TIMEOUT"
    "TEMP_REPO_DIR"
    "FOUND_GIT_DIRS"
)

for var in "${ENV_VARS[@]}"; do
    if [ -n "${!var}" ]; then
        log "Unsetting $var environment variable..."
        unset "$var"
    fi
done
verify_command "Environment variables cleanup" false

log "✅ Cleanup completed successfully!"
log "You can now run setup.sh again for testing."
