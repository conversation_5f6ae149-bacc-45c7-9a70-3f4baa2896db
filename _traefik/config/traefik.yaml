global:
  checkNewVersion: true
  sendAnonymousUsage: false # true by default

# (Optional) Log information
# ---
log:
  #  level: ERROR  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: common # common, json, logfmt
  filePath: /root/logs/traefik.log
  level: DEBUG

# (Optional) Accesslog
# ---
accesslog:
  format: common # common, json, logfmt
  filePath: /root/logs/access.log

# (Optional) Enable API and Dashboard
# ---
api:
  dashboard: true # true by default
  insecure: true # Don't do this in production!
  debug: true

# Entry Points configuration
# ---
entryPoints:
  web:
    address: :80
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
  websecure:
    address: :443
  postgres:
    address: :5432
  mongo:
    address: :27017
  redis:
    address: :6379
  prometheus:
    address: :9090
  # grafana:
  #   address: :3000
  metrics: # <--- ADD THIS
    address: :8082
  alertmanager:
    address: :9093

# Metrics configuration to expose Prometheus metrics
metrics:
  prometheus:
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    entryPoint: metrics

# Configure your CertificateResolver here...
# ---
certificatesResolvers:
  staging:
    acme:
      email: <EMAIL>
      storage: /root/cert/acme.json
      caServer: "https://acme-staging-v02.api.letsencrypt.org/directory"
      httpChallenge:
        entryPoint: web
  production:
    acme:
      email: <EMAIL>
      storage: /root/cert/acme.json
      caServer: "https://acme-v02.api.letsencrypt.org/directory"
      httpChallenge:
        entryPoint: web

providers:
  docker:
    exposedByDefault: false # Default is true
    network: traefik_default # the network these API is used
    defaultRule: Host(`{{.Name}}.${SUBDOMAIN}.aibrainlab.co`)
    endpoint: unix:///var/run/docker.sock
  file:
    # watch for dynamic configuration changes
    directory: /root/files
    watch: true
